# J&T YOU SwiftUI Project - Cursor Rules

## Code Implementation Guidelines

### Context7 MCP Usage
- **ALWAYS** use Context7 MCP (Model Context Protocol) for code implementation within this project
- Before implementing any SwiftUI features, animations, or components:
  1. Use `mcp_context7_resolve-library-id` to find the appropriate SwiftUI library
  2. Use `mcp_context7_get-library-docs` to get up-to-date documentation and best practices
  3. Focus on topics relevant to the specific feature being implemented (e.g., "animations", "transitions", "gestures")

### SwiftUI Best Practices
- Follow modern SwiftUI patterns and conventions
- Use proper state management with @State, @Binding, @ObservedObject, @StateObject
- Implement smooth animations using SwiftUI's animation system
- Structure code with proper separation of concerns
- Use meaningful view modifiers and composition

### Animation Guidelines
- Prefer SwiftUI's built-in animation system (.animation, .transition, withAnimation)
- Use spring animations for natural motion
- Implement proper timing and easing for smooth transitions
- Consider performance implications for complex animations

### Code Quality
- Use descriptive variable and function names
- Add appropriate comments for complex logic
- Follow Swift naming conventions
- Structure views logically with proper hierarchy
- Use MARK comments to organize code sections

### Project Structure
- Keep views in separate files when they become complex
- Use proper file organization
- Follow the existing project structure and naming conventions

## Enforcement
This rule must be followed for ALL code implementation tasks in this project. Any deviation should be justified and documented. 