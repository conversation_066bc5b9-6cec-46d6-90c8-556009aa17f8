# Figma Design URLs for J&T You App

This document contains all the Figma design URLs for the J&T You native SwiftUI iOS application screens.

## Screen Designs

### Splash/Launch Screen
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=1-6123&t=K0hP1hreMNV4ZchK-11
- **Node ID:** 1-6123
- **Description:** Initial splash screen with gradient background and branding

### Login Screen
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=39-1006&t=h7oIoTOJ2gx9HfN8-11
- **Node ID:** 39-1006
- **Description:** Email input screen for authentication

### First Login/OTP Verification
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=39-537&t=h7oIoTOJ2gx9HfN8-11
- **Node ID:** 39-537
- **Description:** OTP verification screen for first-time login

### Dashboard/Main Chat
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=1-3384&t=h7oIoTOJ2gx9HfN8-11
- **Node ID:** 1-3384
- **Description:** Main chat interface dashboard

### Photo Selection (Vyber foto/galerie)
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=39-7502&t=h7oIoTOJ2gx9HfN8-11
- **Node ID:** 39-7502
- **Description:** Photo selection screen for camera/gallery access

### Loading Screen
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=1-3374&t=h7oIoTOJ2gx9HfN8-11
- **Node ID:** 1-3374
- **Description:** Loading screen with progress indicator

### Receipt Generation (Generovana uctenka)
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=1-5038&t=h7oIoTOJ2gx9HfN8-11
- **Node ID:** 1-5038
- **Description:** Generated receipt review screen

### Additional Data (Dalsi udaje)
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=39-5116&t=h7oIoTOJ2gx9HfN8-11
- **Node ID:** 39-5116
- **Description:** Additional data input screen

### Victory Screen
- **URL:** https://www.figma.com/design/c7lUTPF8DdbISy6yFFkBUy/J-T-You?node-id=39-5914&t=h7oIoTOJ2gx9HfN8-11
- **Node ID:** 39-5914
- **Description:** Success/completion screen

## Usage Notes

- All designs are from the same Figma file: `c7lUTPF8DdbISy6yFFkBUy`
- Use the Figma MCP tool to extract specific design details for each SwiftUI screen
- Node IDs can be used directly with the Figma MCP for precise design extraction
- Designs should be implemented pixel-perfect in SwiftUI according to specifications
- Extract colors, typography, spacing, and component specifications for SwiftUI implementation
