// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		2A76CEBA2DE6B4AC005D612B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2A76CEA32DE6B4AB005D612B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2A76CEAA2DE6B4AB005D612B;
			remoteInfo = "J&T YOU";
		};
		2A76CEC42DE6B4AC005D612B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2A76CEA32DE6B4AB005D612B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2A76CEAA2DE6B4AB005D612B;
			remoteInfo = "J&T YOU";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		2A76CEAB2DE6B4AB005D612B /* J&T YOU.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "J&T YOU.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		2A76CEB92DE6B4AC005D612B /* J&T YOUTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "J&T YOUTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		2A76CEC32DE6B4AC005D612B /* J&T YOUUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "J&T YOUUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2A76CEAD2DE6B4AB005D612B /* J&T YOU */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "J&T YOU";
			sourceTree = "<group>";
		};
		2A76CEBC2DE6B4AC005D612B /* J&T YOUTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "J&T YOUTests";
			sourceTree = "<group>";
		};
		2A76CEC62DE6B4AC005D612B /* J&T YOUUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "J&T YOUUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2A76CEA82DE6B4AB005D612B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A76CEB62DE6B4AC005D612B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A76CEC02DE6B4AC005D612B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2A76CEA22DE6B4AB005D612B = {
			isa = PBXGroup;
			children = (
				2A76CEAD2DE6B4AB005D612B /* J&T YOU */,
				2A76CEBC2DE6B4AC005D612B /* J&T YOUTests */,
				2A76CEC62DE6B4AC005D612B /* J&T YOUUITests */,
				2A76CEAC2DE6B4AB005D612B /* Products */,
			);
			sourceTree = "<group>";
		};
		2A76CEAC2DE6B4AB005D612B /* Products */ = {
			isa = PBXGroup;
			children = (
				2A76CEAB2DE6B4AB005D612B /* J&T YOU.app */,
				2A76CEB92DE6B4AC005D612B /* J&T YOUTests.xctest */,
				2A76CEC32DE6B4AC005D612B /* J&T YOUUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2A76CEAA2DE6B4AB005D612B /* J&T YOU */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2A76CECD2DE6B4AC005D612B /* Build configuration list for PBXNativeTarget "J&T YOU" */;
			buildPhases = (
				2A76CEA72DE6B4AB005D612B /* Sources */,
				2A76CEA82DE6B4AB005D612B /* Frameworks */,
				2A76CEA92DE6B4AB005D612B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2A76CEAD2DE6B4AB005D612B /* J&T YOU */,
			);
			name = "J&T YOU";
			packageProductDependencies = (
			);
			productName = "J&T YOU";
			productReference = 2A76CEAB2DE6B4AB005D612B /* J&T YOU.app */;
			productType = "com.apple.product-type.application";
		};
		2A76CEB82DE6B4AC005D612B /* J&T YOUTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2A76CED02DE6B4AC005D612B /* Build configuration list for PBXNativeTarget "J&T YOUTests" */;
			buildPhases = (
				2A76CEB52DE6B4AC005D612B /* Sources */,
				2A76CEB62DE6B4AC005D612B /* Frameworks */,
				2A76CEB72DE6B4AC005D612B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2A76CEBB2DE6B4AC005D612B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2A76CEBC2DE6B4AC005D612B /* J&T YOUTests */,
			);
			name = "J&T YOUTests";
			packageProductDependencies = (
			);
			productName = "J&T YOUTests";
			productReference = 2A76CEB92DE6B4AC005D612B /* J&T YOUTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2A76CEC22DE6B4AC005D612B /* J&T YOUUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2A76CED32DE6B4AC005D612B /* Build configuration list for PBXNativeTarget "J&T YOUUITests" */;
			buildPhases = (
				2A76CEBF2DE6B4AC005D612B /* Sources */,
				2A76CEC02DE6B4AC005D612B /* Frameworks */,
				2A76CEC12DE6B4AC005D612B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2A76CEC52DE6B4AC005D612B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2A76CEC62DE6B4AC005D612B /* J&T YOUUITests */,
			);
			name = "J&T YOUUITests";
			packageProductDependencies = (
			);
			productName = "J&T YOUUITests";
			productReference = 2A76CEC32DE6B4AC005D612B /* J&T YOUUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2A76CEA32DE6B4AB005D612B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					2A76CEAA2DE6B4AB005D612B = {
						CreatedOnToolsVersion = 16.3;
					};
					2A76CEB82DE6B4AC005D612B = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 2A76CEAA2DE6B4AB005D612B;
					};
					2A76CEC22DE6B4AC005D612B = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 2A76CEAA2DE6B4AB005D612B;
					};
				};
			};
			buildConfigurationList = 2A76CEA62DE6B4AB005D612B /* Build configuration list for PBXProject "J&T YOU" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2A76CEA22DE6B4AB005D612B;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2A76CEAC2DE6B4AB005D612B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2A76CEAA2DE6B4AB005D612B /* J&T YOU */,
				2A76CEB82DE6B4AC005D612B /* J&T YOUTests */,
				2A76CEC22DE6B4AC005D612B /* J&T YOUUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2A76CEA92DE6B4AB005D612B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A76CEB72DE6B4AC005D612B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A76CEC12DE6B4AC005D612B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2A76CEA72DE6B4AB005D612B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A76CEB52DE6B4AC005D612B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A76CEBF2DE6B4AC005D612B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2A76CEBB2DE6B4AC005D612B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2A76CEAA2DE6B4AB005D612B /* J&T YOU */;
			targetProxy = 2A76CEBA2DE6B4AC005D612B /* PBXContainerItemProxy */;
		};
		2A76CEC52DE6B4AC005D612B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2A76CEAA2DE6B4AB005D612B /* J&T YOU */;
			targetProxy = 2A76CEC42DE6B4AC005D612B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2A76CECB2DE6B4AC005D612B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = G97V29W42M;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2A76CECC2DE6B4AC005D612B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = G97V29W42M;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		2A76CECE2DE6B4AC005D612B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "J&T YOU/J_T_YOU.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = G97V29W42M;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.michalkomar.you;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		2A76CECF2DE6B4AC005D612B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "J&T YOU/J_T_YOU.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = G97V29W42M;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.michalkomar.you;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		2A76CED12DE6B4AC005D612B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = G97V29W42M;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.michalkomar.J-T-YOUTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/J&T YOU.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/J&T YOU";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		2A76CED22DE6B4AC005D612B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = G97V29W42M;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.michalkomar.J-T-YOUTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/J&T YOU.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/J&T YOU";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		2A76CED42DE6B4AC005D612B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = G97V29W42M;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.michalkomar.J-T-YOUUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "J&T YOU";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		2A76CED52DE6B4AC005D612B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = G97V29W42M;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.michalkomar.J-T-YOUUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "J&T YOU";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2A76CEA62DE6B4AB005D612B /* Build configuration list for PBXProject "J&T YOU" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A76CECB2DE6B4AC005D612B /* Debug */,
				2A76CECC2DE6B4AC005D612B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2A76CECD2DE6B4AC005D612B /* Build configuration list for PBXNativeTarget "J&T YOU" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A76CECE2DE6B4AC005D612B /* Debug */,
				2A76CECF2DE6B4AC005D612B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2A76CED02DE6B4AC005D612B /* Build configuration list for PBXNativeTarget "J&T YOUTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A76CED12DE6B4AC005D612B /* Debug */,
				2A76CED22DE6B4AC005D612B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2A76CED32DE6B4AC005D612B /* Build configuration list for PBXNativeTarget "J&T YOUUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A76CED42DE6B4AC005D612B /* Debug */,
				2A76CED52DE6B4AC005D612B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2A76CEA32DE6B4AB005D612B /* Project object */;
}
