# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**J&T YOU** is a native SwiftUI iOS application for LLM-driven tools with secure Azure BFF communication. This targets iPhone and iPad with iOS 17.6+ deployment target.

## Development Commands

### Building and Running
```bash
# Build the project
xcodebuild -project "J&T YOU.xcodeproj" -scheme "J&T YOU" build

# Run tests
xcodebuild test -project "J&T YOU.xcodeproj" -scheme "J&T YOU" -destination 'platform=iOS Simulator,name=iPhone 15'

# Run UI tests specifically
xcodebuild test -project "J&T YOU.xcodeproj" -scheme "J&T YOU" -destination 'platform=iOS Simulator,name=iPhone 15' -only-testing:"J&<PERSON> YOUUITests"

# Archive for distribution
xcodebuild archive -project "J&T YOU.xcodeproj" -scheme "J&T YOU" -archivePath "build/J&T YOU.xcarchive"
```

### Testing Framework
- **Unit Tests**: Uses modern Swift Testing framework (not XCTest)
- **UI Tests**: Uses XCTest with launch performance testing
- Run single test by using `-only-testing:` flag with specific test target

## Architecture & Code Structure

### Current State
- **Framework**: Native SwiftUI with iOS SDK 17.6+
- **Architecture**: Basic SwiftUI app structure (ready for MVVM implementation)
- **Entry Point**: `J_T_YOUApp.swift` using `@main` App protocol
- **Main View**: `ContentView.swift` (currently template, needs chat interface implementation)

### Planned Architecture (per PRD.md)
```
J&T YOU/
├── Views/
│   ├── Authentication/     # Login, OTP verification screens
│   ├── Chat/              # Main chat interface, history
│   ├── Profile/           # User profile, settings
│   └── Common/            # Reusable UI components
├── Models/                # Data models for API responses
├── Services/
│   ├── Authentication/   # Azure AD OTP authentication
│   ├── Networking/       # Azure BFF communication
│   └── Storage/          # iOS Keychain, UserDefaults
├── ViewModels/           # MVVM view models using ObservableObject
└── Assets.xcassets/      # App icons, colors, images
```

### Key Technical Requirements
- **Authentication**: Azure AD with email OTP, stored in iOS Keychain Services
- **Networking**: URLSession for Azure BFF communication (OpenAI-compatible endpoints)
- **Chat UI**: Custom SwiftUI components with ScrollView and LazyVStack
- **Media**: PhotosUI, Camera, AVFoundation for photo/document upload
- **Navigation**: NavigationStack for iOS 16+ (adaptive for iPhone and iPad)

## Design Implementation

### Figma Integration
- Design file ID: `c7lUTPF8DdbISy6yFFkBUy`
- Use Figma MCP for extracting precise design specifications
- Implement pixel-perfect SwiftUI layouts following Human Interface Guidelines
- Extract colors, typography, spacing for SwiftUI components

### Key Screens (from figma_design.md)
1. Splash/Launch Screen (Node: 1-6123)
2. Login Screen (Node: 39-1006)
3. OTP Verification (Node: 39-537)
4. Main Chat Dashboard (Node: 1-3384)
5. Photo Selection (Node: 39-7502)
6. Receipt Generation (Node: 1-5038)

## Development Workflow

### Project Configuration
- **Bundle ID**: com.michalkomar.J-T-YOU
- **Target Platform**: iPhone and iPad
- **Code Signing**: Automatic with hardened runtime
- **Asset Catalog**: Includes AppIcon and AccentColor
- **Capabilities**: App Sandbox enabled with file read access

### Testing Strategy
- Unit tests in `J&T YOUTests/` using Swift Testing framework
- UI tests in `J&T YOUUITests/` using XCTest
- Launch performance testing configured
- SwiftUI Preview testing for component validation

## API Integration

### Azure BFF Endpoints (from PRD.md)
- `POST /auth/otp/request` - Request OTP for email
- `POST /auth/otp/verify` - Verify OTP and get token
- `POST /v1/chat/completions` - OpenAI-compatible chat endpoint
- `GET /user/profile` - User information
- `POST /upload` - Media file uploads

### Security Requirements
- All HTTPS communication with URLSession
- Token storage in iOS Keychain Services
- Whitelisted email validation
- Quick-expiring OTP tokens

## AI Tools Integration

### Recommended MCP Tools
- **Context7 MCP**: For SwiftUI code suggestions and architecture guidance
- **Figma MCP**: For design extraction and implementation
- Use node IDs from figma_design.md for precise design extraction

### Development Approach
- Follow SwiftUI best practices and iOS Human Interface Guidelines
- Implement MVVM pattern with ObservableObject for state management
- Use GeometryReader sparingly, rely on SwiftUI's automatic layout
- Ensure adaptive design for iPhone and iPad screen sizes and interactions