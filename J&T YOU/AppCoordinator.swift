//
//  AppCoordinator.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

@Observable
class AppCoordinator {
    // MARK: - State Properties
    
    var appState: AppState = .splash
    var isTransitioning: Bool = false
    
    // MARK: - App States
    
    enum AppState: CaseIterable {
        case splash
        case main
    }
    
    // MARK: - Methods
    
    func startApp() {
        // Automatically transition from splash to main after animation completes
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.8) {
            self.transitionToMain()
        }
    }
    
    func transitionToMain() {
        withAnimation(.easeInOut(duration: 0.8)) {
            isTransitioning = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeInOut(duration: 0.8)) {
                self.appState = .main
                self.isTransitioning = false
            }
        }
    }
    
    func resetToSplash() {
        withAnimation(.easeInOut(duration: 0.5)) {
            appState = .splash
            isTransitioning = false
        }
    }
}

// MARK: - App Coordinator View

struct AppCoordinatorView: View {
    @State private var coordinator = AppCoordinator()
    
    var body: some View {
        ZStack {
            switch coordinator.appState {
            case .splash:
                SplashScreenView()
                    .transition(.opacity)
                    .onAppear {
                        coordinator.startApp()
                    }
                
            case .main:
                MainAppView()
                    .transition(.opacity)
            }
            
            // Transition overlay
            if coordinator.isTransitioning {
                Color.black
                    .ignoresSafeArea(.all, edges: .all)
                    .opacity(0.3)
                    .transition(.opacity)
            }
        }
        .animation(.easeInOut(duration: 0.8), value: coordinator.appState)
        .animation(.easeInOut(duration: 0.4), value: coordinator.isTransitioning)
    }
} 