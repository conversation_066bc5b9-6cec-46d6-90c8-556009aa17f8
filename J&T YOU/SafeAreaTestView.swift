//
//  SafeAreaTestView.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

// MARK: - Safe Area Test View

/// A test view to verify safe area implementation across different devices and orientations
struct SafeAreaTestView: View {
    @State private var showSafeAreaGuides = true
    @State private var currentDevice = "Current Device"
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                DesignSystemComponents.AppBackground()
                
                // Safe area visualization
                if showSafeAreaGuides {
                    safeAreaGuides(geometry: geometry)
                }
                
                // Main content
                VStack(spacing: DesignSystem.Spacing.lg) {
                    // Top content area
                    topContentArea(geometry: geometry)
                    
                    Spacer()
                    
                    // Center content
                    centerContentArea
                    
                    Spacer()
                    
                    // Bottom content area
                    bottomContentArea
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                
                // Controls overlay
                controlsOverlay
            }
        }
        .ignoresSafeArea(.container, edges: .horizontal)
        .onAppear {
            updateDeviceInfo()
        }
    }
}

// MARK: - View Components

extension SafeAreaTestView {
    
    private func safeAreaGuides(geometry: GeometryProxy) -> some View {
        ZStack {
            // Safe area outline
            Rectangle()
                .stroke(Color.red, lineWidth: 2)
                .frame(
                    width: geometry.size.width - geometry.safeAreaInsets.leading - geometry.safeAreaInsets.trailing,
                    height: geometry.size.height - geometry.safeAreaInsets.top - geometry.safeAreaInsets.bottom
                )
                .offset(
                    x: (geometry.safeAreaInsets.leading - geometry.safeAreaInsets.trailing) / 2,
                    y: (geometry.safeAreaInsets.top - geometry.safeAreaInsets.bottom) / 2
                )
            
            // Safe area inset indicators
            VStack {
                if geometry.safeAreaInsets.top > 0 {
                    Rectangle()
                        .fill(Color.red.opacity(0.3))
                        .frame(height: geometry.safeAreaInsets.top)
                        .overlay(
                            Text("Top: \(Int(geometry.safeAreaInsets.top))pt")
                                .font(.caption)
                                .foregroundColor(.white)
                        )
                }
                
                Spacer()
                
                if geometry.safeAreaInsets.bottom > 0 {
                    Rectangle()
                        .fill(Color.red.opacity(0.3))
                        .frame(height: geometry.safeAreaInsets.bottom)
                        .overlay(
                            Text("Bottom: \(Int(geometry.safeAreaInsets.bottom))pt")
                                .font(.caption)
                                .foregroundColor(.white)
                        )
                }
            }
            
            HStack {
                if geometry.safeAreaInsets.leading > 0 {
                    Rectangle()
                        .fill(Color.red.opacity(0.3))
                        .frame(width: geometry.safeAreaInsets.leading)
                        .overlay(
                            Text("L: \(Int(geometry.safeAreaInsets.leading))")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .rotationEffect(.degrees(-90))
                        )
                }
                
                Spacer()
                
                if geometry.safeAreaInsets.trailing > 0 {
                    Rectangle()
                        .fill(Color.red.opacity(0.3))
                        .frame(width: geometry.safeAreaInsets.trailing)
                        .overlay(
                            Text("R: \(Int(geometry.safeAreaInsets.trailing))")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .rotationEffect(.degrees(90))
                        )
                }
            }
        }
    }
    
    private func topContentArea(geometry: GeometryProxy) -> some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            Text("Safe Area Test")
                .font(DesignSystem.Typography.headlineFont)
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Text(currentDevice)
                .font(DesignSystem.Typography.bodyFont)
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Text("Screen: \(Int(geometry.size.width)) × \(Int(geometry.size.height))")
                .font(DesignSystem.Typography.caption1Font)
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .safeAreaInset(edge: .top) {
            Color.clear.frame(height: DesignSystem.Spacing.lg)
        }
    }
    
    private var centerContentArea: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            DesignSystemComponents.GlassmorphicContainer {
                VStack(spacing: DesignSystem.Spacing.sm) {
                    Text("Native Safe Area Implementation")
                        .font(DesignSystem.Typography.subheadlineFont)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("This content automatically adapts to different device safe areas using native iOS APIs.")
                        .font(DesignSystem.Typography.bodyFont)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(DesignSystem.Spacing.lg)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
        }
    }
    
    private var bottomContentArea: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            DesignSystemComponents.GlassmorphicContainer {
                HStack(spacing: DesignSystem.Spacing.lg) {
                    DesignSystemComponents.CircularIconButton(icon: "arrow.left") {
                        // Test button
                    }
                    
                    Text("Bottom Input Area")
                        .font(DesignSystem.Typography.bodyFont)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    DesignSystemComponents.CircularIconButton(icon: "arrow.right") {
                        // Test button
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.md)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: DesignSystem.Spacing.sm)
        }
    }
    
    private var controlsOverlay: some View {
        VStack {
            HStack {
                Spacer()
                
                Button(action: {
                    showSafeAreaGuides.toggle()
                }) {
                    Image(systemName: showSafeAreaGuides ? "eye.slash" : "eye")
                        .font(.title2)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .padding(DesignSystem.Spacing.sm)
                        .background(DesignSystem.BlurEffects.thin)
                        .clipShape(Circle())
                }
                .padding(.trailing, DesignSystem.Spacing.lg)
            }
            .safeAreaInset(edge: .top) {
                Color.clear.frame(height: DesignSystem.Spacing.lg)
            }
            
            Spacer()
        }
    }
    
    private func updateDeviceInfo() {
        let device = UIDevice.current
        currentDevice = "\(device.model) - iOS \(device.systemVersion)"
    }
}

// MARK: - Preview

#Preview {
    SafeAreaTestView()
}
