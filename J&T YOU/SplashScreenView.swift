//
//  SplashScreenView.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

struct SplashScreenView: View {
    // MARK: - State Properties

    @State private var animationPhase: AnimationPhase = .initial
    @State private var letterSpacing: CGFloat = 6
    @State private var logoOpacity: Double = 0.6
    @State private var logoScale: CGFloat = 1.0
    @State private var backgroundBlur: CGFloat = 100
    @State private var contentOffset: CGFloat = 0

    // MARK: - Animation Phases

    enum AnimationPhase: CaseIterable {
        case initial
        case letterSpacingReduction
        case logoConsolidation
        case finalState
        case complete
    }

    // MARK: - Body

    var body: some View {
        ZStack {
            // Background with gradient effect
            backgroundView

            // Main content
            mainContentView
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black)
        .ignoresSafeArea(.all, edges: .all)
        .onAppear {
            startAnimationSequence()
        }
    }

    // MARK: - Background View

    private var backgroundView: some View {
        ZStack {
            // Main teal background with animated blur
            Rectangle()
                .foregroundColor(.clear)
                .background(Color(red: 0.01, green: 0.65, blue: 0.57))
                .blur(radius: backgroundBlur)
                .animation(.easeInOut(duration: 2.0), value: backgroundBlur)

            // Secondary overlay with blur
            Rectangle()
                .foregroundColor(.clear)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .blur(radius: 84)
                .opacity(0.8)
        }
    }



    // MARK: - Main Content View

    private var mainContentView: some View {
        VStack(spacing: 8) {
            // "YOU" text with sophisticated animation
            Text(animationPhase == .initial ? "Y O U" : "YOU")
                .font(.system(size: 48, weight: .bold, design: .default))
                .tracking(letterSpacing)
                .foregroundColor(.white)
                .scaleEffect(logoScale)
                .offset(y: contentOffset)
                .animation(.spring(response: 1.2, dampingFraction: 0.8, blendDuration: 0), value: animationPhase)
                .animation(.spring(response: 1.0, dampingFraction: 0.7, blendDuration: 0), value: letterSpacing)
                .animation(.spring(response: 1.0, dampingFraction: 0.7, blendDuration: 0), value: logoScale)
                .animation(.spring(response: 0.8, dampingFraction: 0.9, blendDuration: 0), value: contentOffset)

            // "by J&T" subtitle with enhanced animation
            HStack(spacing: 8) {
                Text("by")
                    .font(.system(size: 25, weight: .medium, design: .default))
                    .italic()
                    .foregroundColor(.white)

                // J&T logo placeholder with rounded corners
                RoundedRectangle(cornerRadius: 4)
                    .foregroundColor(.clear)
                    .frame(width: 43, height: 19)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color(red: 0.50, green: 0.23, blue: 0.27).opacity(0.50))
                    )
                    .overlay(
                        Text("J&T")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white.opacity(0.8))
                    )
            }
            .opacity(logoOpacity)
            .scaleEffect(animationPhase == .complete ? 1.05 : 1.0)
            .animation(.spring(response: 1.0, dampingFraction: 0.8, blendDuration: 0), value: logoOpacity)
            .animation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0), value: animationPhase)
        }
    }



    // MARK: - Animation Methods

    private func startAnimationSequence() {
        // Phase 1: Initial delay and letter spacing reduction
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.spring(response: 1.2, dampingFraction: 0.8, blendDuration: 0)) {
                animationPhase = .letterSpacingReduction
                letterSpacing = 0
                logoScale = 1.1
                backgroundBlur = 80
            }
        }

        // Phase 2: Logo consolidation
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation(.spring(response: 1.0, dampingFraction: 0.7, blendDuration: 0)) {
                animationPhase = .logoConsolidation
                logoOpacity = 1.0
                logoScale = 1.0
                contentOffset = -10
            }
        }

        // Phase 3: Final state with subtle bounce
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6, blendDuration: 0)) {
                animationPhase = .finalState
                contentOffset = 0
                backgroundBlur = 60
            }
        }

        // Phase 4: Complete animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.2) {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
                animationPhase = .complete
            }
        }
    }
}

// MARK: - Preview

#Preview {
    SplashScreenView()
}