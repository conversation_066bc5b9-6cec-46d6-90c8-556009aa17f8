# Product Requirements Document (PRD)
## J&T YOU - Native SwiftUI iOS Application

### 1. Product Overview

**Product Name:** J&T YOU  
**Platform:** Native iOS (SwiftUI)  
**Purpose:** Single point of contact for LLM-driven tools with secure Azure BFF communication  

### 2. Objectives

- Create a modern, pixel-perfect native SwiftUI iOS application
- Provide unified interface for LLM-driven tools
- Implement secure communication with Azure-hosted Backend for Frontend (BFF)
- Leverage native iOS performance and platform capabilities
- Enable secure authentication using Azure AD email OTP for whitelisted users

### 3. Target Platform

- **Platform:** iPhone and iPad (App Store)
- **Framework:** Native SwiftUI with iOS SDK 17.6+
- **Architecture:** Native iOS with SwiftUI declarative UI framework

### 4. Core Features

#### 4.1 Authentication System
- **Method:** Azure Active Directory (Azure AD) with email one-time passcode (OTP)
- **User Base:** Whitelisted email addresses only
- **Flow:** 
  - Email input → OTP request → OTP verification → Secure token storage
- **Security:** Tokens stored using iOS Keychain Services
- **Guest Users:** Azure AD guest user invitations with OTP enabled

#### 4.2 Chat Interface
- **UI Framework:** Native SwiftUI with custom chat components
- **Communication:** OpenAI API-compatible protocol with BFF using URLSession
- **Media Capabilities:**
  - Photo capture (Camera framework)
  - Image selection (PhotosUI framework)
  - Document upload (UniformTypeIdentifiers)
- **Native iOS Integration:** PhotosUI, Camera, FileProvider frameworks

#### 4.3 Chat History
- **Organization:** Grouped by days, weeks, months, and years
- **UI Component:** SwiftUI List with ForEach and Section for efficient rendering
- **Backend:** Azure Cosmos DB for scalable storage
- **UX:** Dynamic grouping based on conversation volume and recency

#### 4.4 User Profile
- **Data Source:** BFF endpoint (GET /user/profile)
- **Display:** User information according to Figma design
- **Features:** Basic settings and logout functionality

### 5. Technical Architecture

#### 5.1 Frontend Stack
- **Framework:** Native SwiftUI with iOS SDK 17.6+
- **Navigation:** NavigationStack and NavigationSplitView
- **Chat UI:** Custom SwiftUI components with ScrollView and LazyVStack
- **Authentication:** AuthenticationServices framework
- **Storage:** iOS Keychain Services and UserDefaults
- **Media:** PhotosUI, Camera, AVFoundation, UniformTypeIdentifiers

#### 5.2 Backend Integration
- **Platform:** Azure Functions/App Service
- **Database:** Azure Cosmos DB
- **Authentication:** Azure AD token validation
- **API Protocol:** OpenAI-compatible endpoints
- **Communication:** HTTPS with Authorization headers

#### 5.3 Security Requirements
- All communications over HTTPS with URLSession
- Secure token storage with iOS Keychain Services
- Input validation on BFF endpoints
- Quick-expiring OTP tokens
- Whitelisted email validation

### 6. User Experience Requirements

#### 6.1 Design Standards
- **Source:** Pixel-perfect implementation from Figma design
- **Extraction:** Figma MCP or manual specification extraction
- **Responsiveness:** SwiftUI automatic layout with GeometryReader when needed
- **Consistency:** iOS-native design patterns and Human Interface Guidelines

#### 6.2 Performance Requirements
- Smooth chat interface with real-time messaging
- Efficient chat history loading with pagination
- Optimized media upload handling
- Responsive navigation between screens

### 7. Development Approach

#### 7.1 AI Tools Integration
- **Primary:** Claude Code for SwiftUI development acceleration
- **Context Awareness:** Context7 MCP for code suggestions and architecture guidance
- **Design Extraction:** Figma MCP for precise design implementation
- **Testing:** XCTest framework for unit and UI testing

#### 7.2 Project Structure
```
J&T YOU/
├── J&T YOU/
│   ├── Views/
│   │   ├── Authentication/
│   │   ├── Chat/
│   │   ├── Profile/
│   │   └── Common/
│   ├── Models/
│   ├── Services/
│   │   ├── Authentication/
│   │   ├── Networking/
│   │   └── Storage/
│   ├── ViewModels/
│   └── Assets.xcassets/
├── J&T YOU.xcodeproj
└── J&T YOUTests/
```

### 8. API Endpoints

#### 8.1 Authentication
- `POST /auth/otp/request` - Request OTP for email
- `POST /auth/otp/verify` - Verify OTP and get token
- `POST /auth/refresh` - Refresh access token

#### 8.2 Chat
- `POST /v1/chat/completions` - Send message, receive LLM response
- `POST /upload` - Upload media files
- `GET /history` - Retrieve chat history with date filtering

#### 8.3 User
- `GET /user/profile` - Fetch user information
- `PUT /user/settings` - Update user preferences

### 9. Testing Strategy

#### 9.1 iOS Testing
- XCTest for unit testing SwiftUI views and ViewModels
- XCUITest for end-to-end testing on simulators and devices
- SwiftUI Preview testing for UI component validation
- App Store compliance validation
- iOS performance profiling with Instruments

### 10. Deployment Requirements

#### 10.1 iOS Deployment
- Build using Xcode Archive or xcodebuild
- App Store submission via App Store Connect
- Apple guidelines compliance
- iOS code signing and provisioning profiles

### 11. Success Criteria

- Pixel-perfect design implementation matching Figma specifications
- Secure authentication with Azure AD OTP for whitelisted users
- Seamless chat experience with media upload capabilities
- Organized chat history with intuitive navigation
- Native SwiftUI performance and user experience
- Successful App Store approval

### 12. Future Considerations

- Offline support with local caching
- Push notifications for new messages
- Advanced user settings and preferences
- Multi-language support
- Enhanced iOS accessibility features
- iPad-specific optimizations
- iOS Widgets and App Extensions
- Siri Shortcuts integration
